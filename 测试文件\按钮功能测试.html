<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            border: none;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #2980b9;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>横道图系统按钮功能测试</h1>
        
        <h3>测试说明</h3>
        <p>点击下面的按钮来测试主页面的按钮功能是否正常工作：</p>
        
        <div>
            <button class="test-button" onclick="testAddTaskButton()">测试添加任务按钮</button>
            <button class="test-button" onclick="testAddGroupButton()">测试创建分组按钮</button>
            <button class="test-button" onclick="openMainPage()">打开主页面</button>
        </div>
        
        <div id="results"></div>
        
        <h3>手动测试步骤</h3>
        <ol>
            <li>点击"打开主页面"按钮</li>
            <li>在主页面中点击左侧的"添加任务"按钮</li>
            <li>在主页面中点击左侧的"创建分组"按钮</li>
            <li>检查是否出现相应的提示框</li>
        </ol>
        
        <h3>预期结果</h3>
        <ul>
            <li>点击"添加任务"应该弹出输入任务名称的提示框</li>
            <li>点击"创建分组"应该弹出输入分组名称的提示框</li>
            <li>成功添加任务后，甘特图应该更新显示新任务</li>
        </ul>
    </div>

    <script>
        function addResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function testAddTaskButton() {
            try {
                // 模拟测试添加任务功能
                const taskName = prompt('请输入任务名称（测试）：', '测试任务');
                if (taskName) {
                    addResult(`✅ 添加任务功能正常 - 输入的任务名称：${taskName}`);
                } else {
                    addResult('⚠️ 用户取消了添加任务操作');
                }
            } catch (error) {
                addResult(`❌ 添加任务功能测试失败：${error.message}`, false);
            }
        }

        function testAddGroupButton() {
            try {
                // 模拟测试创建分组功能
                const groupName = prompt('请输入分组名称（测试）：', '测试分组');
                if (groupName) {
                    addResult(`✅ 创建分组功能正常 - 输入的分组名称：${groupName}`);
                } else {
                    addResult('⚠️ 用户取消了创建分组操作');
                }
            } catch (error) {
                addResult(`❌ 创建分组功能测试失败：${error.message}`, false);
            }
        }

        function openMainPage() {
            window.open('http://localhost:3001', '_blank');
            addResult('✅ 已打开主页面，请在新窗口中测试按钮功能');
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            addResult('🔍 按钮功能测试页面已加载，可以开始测试');
        };
    </script>
</body>
</html>

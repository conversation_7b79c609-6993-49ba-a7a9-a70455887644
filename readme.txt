
项目名称：施工进度甘特图管理系统

项目描述：
这是一个基于Web的施工进度甘特图管理系统，使用HTML、CSS和JavaScript开发。系统提供了完整的任务管理功能，包括添加、编辑、删除任务，任务分组，任务拖拽调整时间，任务条样式自定义，以及Excel导入导出功能。

主要功能：
1. 任务管理：
   - 添加、编辑、删除任务
   - 任务分组管理
   - 任务时间设置（开始时间、持续时间自动计算结束时间）
   - 任务颜色自定义

2. 任务操作：
   - 任务选择（单选、多选、全选）
   - 任务上下移动
   - 任务复制
   - 任务批量分组

3. 甘特图显示：
   - 可视化甘特图展示
   - 时间标尺（支持自定义步长）
   - 任务条拖拽调整（左右移动、调整长度）
   - 任务条样式自定义（边框颜色、宽度、任务条高度、透明度）

4. 界面设置：
   - 列显示设置（可选择显示/隐藏特定列）
   - 列宽调整（可拖拽调整列宽）

5. 数据管理：
   - 本地存储（使用localStorage保存数据）
   - Excel导入导出功能

6. 其他功能：
   - 响应式设计
   - 任务条标签显示
   - 日期范围自动调整



开发提示词：
1. 创建一个包含任务管理功能的甘特图系统
2. 实现任务的增删改查和分组功能
3. 开发甘特图可视化组件，支持时间轴显示
4. 实现任务条的拖拽交互（移动和调整长度）
5. 添加任务样式自定义功能（颜色、边框、透明度等）
6. 实现Excel导入导出功能
7. 添加界面自定义功能（列显示设置、列宽调整）
8. 使用localStorage进行数据持久化
9. 确保响应式设计和良好的用户体验
10. 实现任务的批量操作功能（批量分组、移动等）
11. 创建一个Node.js服务器来托管应用
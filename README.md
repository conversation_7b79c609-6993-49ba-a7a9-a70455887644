
# 施工进度甘特图管理系统

一个基于Web的施工进度甘特图管理系统，用于项目进度规划、任务管理和可视化展示。

## 功能特性

### 🎯 核心功能
- **任务管理**：快速添加、编辑和删除任务
- **分组管理**：创建和管理任务分组
- **甘特图显示**：直观的时间轴和任务条展示
- **数据导入导出**：支持Excel文件导入导出
- **响应式设计**：适配不同屏幕尺寸

### 📊 甘特图特性
- **多列显示**：分组名、编号、任务名称、持续时间、开始时间、结束时间
- **可视化时间轴**：月视图、周视图、日视图切换
- **任务条交互**：拖拽移动、调整大小
- **颜色编码**：不同任务使用不同颜色区分

### 🛠 操作界面
- **简洁左侧面板**：快捷操作按钮
- **详细甘特图**：完整的项目时间线视图
- **状态提示**：实时操作反馈
- **模态框交互**：友好的用户输入界面

## 技术栈

- **前端**：HTML5, CSS3, JavaScript (ES6+)
- **后端**：Node.js, Express.js
- **样式**：CSS Grid, Flexbox, 响应式设计
- **图标**：Font Awesome
- **数据处理**：SheetJS (Excel导入导出)

## 快速开始

### 环境要求
- Node.js 14.0 或更高版本
- npm 6.0 或更高版本

### 安装步骤

1. **克隆项目**
```bash
git clone https://gitee.com/better319/horizontal-chart-2.git
cd horizontal-chart-2
```

2. **安装依赖**
```bash
npm install
```

3. **启动服务器**
```bash
npm start
# 或者
node server.js
```

4. **访问应用**
打开浏览器访问：http://localhost:3001

## 使用说明

### 添加任务
1. 点击左侧面板的"添加任务"按钮
2. 依次输入：任务名称、分组、开始日期、持续时间
3. 系统自动生成任务编号和颜色
4. 任务将立即显示在甘特图中

### 创建分组
1. 点击左侧面板的"创建分组"按钮
2. 输入新的分组名称
3. 在添加任务时可以使用该分组

### 导入导出
- **导入**：点击工具栏"导入Excel"按钮，选择Excel文件
- **导出**：点击工具栏"导出Excel"按钮，下载项目数据

### 视图切换
- 使用甘特图右上角的下拉菜单切换时间视图
- 支持日视图、周视图、月视图

## 项目结构

```
horizontal-chart-2/
├── index.html              # 主页面文件
├── server.js               # Express服务器
├── package.json            # 项目配置和依赖
├── package-lock.json       # 依赖锁定文件
├── README.md              # 项目说明文档
├── 安装和使用指南.md        # 详细使用指南
├── 测试文件/               # 测试相关文件
│   ├── 功能测试说明.md
│   ├── 测试页面.html
│   ├── 验证脚本.js
│   ├── 修改总结.md
│   ├── 按钮功能测试.html
│   └── 按钮功能修复总结.md
└── node_modules/          # 依赖包目录
```

## 更新日志

### v2.0.0 (最新版本)
- ✅ 重构左侧面板为快捷操作界面
- ✅ 新增甘特图多列显示功能
- ✅ 优化任务添加和分组创建流程
- ✅ 修复按钮功能问题
- ✅ 改进响应式设计
- ✅ 删除冗余功能，提升用户体验

### v1.0.0
- 基础甘特图功能
- 任务管理界面
- 数据导入导出
- 响应式设计

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目地址：https://gitee.com/better319/horizontal-chart-2
- 问题反馈：请在Gitee上提交Issue

## 致谢

感谢所有为这个项目做出贡献的开发者！

